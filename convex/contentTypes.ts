import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all content types
export const getAllContentTypes = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("contentTypes").collect();
  },
});

// Get content type by name
export const getContentTypeByName = query({
  args: { name: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", args.name))
      .first();
  },
});

// Get content types by category
export const getContentTypesByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("contentTypes")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .collect();
  },
});

// Create content type
export const createContentType = mutation({
  args: {
    name: v.string(),
    label: v.string(),
    description: v.string(),
    icon: v.optional(v.string()),
    category: v.string(),
    fields: v.array(v.object({
      name: v.string(),
      label: v.string(),
      type: v.union(
        v.literal("text"),
        v.literal("richText"),
        v.literal("image"),
        v.literal("boolean"),
        v.literal("number"),
        v.literal("array"),
        v.literal("object"),
        v.literal("select"),
        v.literal("multiSelect"),
        v.literal("date"),
        v.literal("url"),
        v.literal("email"),
        v.literal("color")
      ),
      required: v.boolean(),
      description: v.optional(v.string()),
      placeholder: v.optional(v.string()),
      defaultValue: v.optional(v.any()),
      validation: v.optional(v.object({
        min: v.optional(v.number()),
        max: v.optional(v.number()),
        pattern: v.optional(v.string()),
        options: v.optional(v.array(v.string())),
      })),
    })),
    settings: v.optional(v.object({
      allowMultiple: v.boolean(),
      isSystem: v.boolean(),
      sortable: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    // TODO: Add admin authentication check
    
    // Check if content type with this name already exists
    const existing = await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", args.name))
      .first();

    if (existing) {
      throw new Error(`Content type with name "${args.name}" already exists`);
    }

    const now = Date.now();
    
    return await ctx.db.insert("contentTypes", {
      name: args.name,
      label: args.label,
      description: args.description,
      icon: args.icon,
      category: args.category,
      fields: args.fields,
      settings: args.settings || {
        allowMultiple: true,
        isSystem: false,
        sortable: true,
      },
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update content type
export const updateContentType = mutation({
  args: {
    id: v.id("contentTypes"),
    label: v.optional(v.string()),
    description: v.optional(v.string()),
    icon: v.optional(v.string()),
    category: v.optional(v.string()),
    fields: v.optional(v.array(v.object({
      name: v.string(),
      label: v.string(),
      type: v.union(
        v.literal("text"),
        v.literal("richText"),
        v.literal("image"),
        v.literal("boolean"),
        v.literal("number"),
        v.literal("array"),
        v.literal("object"),
        v.literal("select"),
        v.literal("multiSelect"),
        v.literal("date"),
        v.literal("url"),
        v.literal("email"),
        v.literal("color")
      ),
      required: v.boolean(),
      description: v.optional(v.string()),
      placeholder: v.optional(v.string()),
      defaultValue: v.optional(v.any()),
      validation: v.optional(v.object({
        min: v.optional(v.number()),
        max: v.optional(v.number()),
        pattern: v.optional(v.string()),
        options: v.optional(v.array(v.string())),
      })),
    }))),
    settings: v.optional(v.object({
      allowMultiple: v.boolean(),
      isSystem: v.boolean(),
      sortable: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    // TODO: Add admin authentication check
    
    const contentType = await ctx.db.get(args.id);
    if (!contentType) {
      throw new Error("Content type not found");
    }

    // Prevent updating system content types
    if (contentType.settings?.isSystem) {
      throw new Error("Cannot update system content types");
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.label !== undefined) updates.label = args.label;
    if (args.description !== undefined) updates.description = args.description;
    if (args.icon !== undefined) updates.icon = args.icon;
    if (args.category !== undefined) updates.category = args.category;
    if (args.fields !== undefined) updates.fields = args.fields;
    if (args.settings !== undefined) updates.settings = args.settings;

    return await ctx.db.patch(args.id, updates);
  },
});

// Get content type by ID
export const getContentTypeById = query({
  args: { id: v.id("contentTypes") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Delete content type
export const deleteContentType = mutation({
  args: { id: v.id("contentTypes") },
  handler: async (ctx, args) => {
    // TODO: Add admin authentication check
    
    const contentType = await ctx.db.get(args.id);
    if (!contentType) {
      throw new Error("Content type not found");
    }

    // Prevent deleting system content types
    if (contentType.settings?.isSystem) {
      throw new Error("Cannot delete system content types");
    }

    // Check if there are any content instances using this type
    const contentInstances = await ctx.db
      .query("content")
      .filter((q) => q.eq(q.field("contentTypeId"), args.id))
      .collect();

    if (contentInstances.length > 0) {
      throw new Error("Cannot delete content type that has content instances. Delete all content instances first.");
    }

    return await ctx.db.delete(args.id);
  },
});

// Initialize predefined content types
export const initializePredefinedContentTypes = mutation({
  args: {},
  handler: async (ctx) => {
    // TODO: Add admin authentication check

    const predefinedTypes = [
      {
        name: "hero_section",
        label: "Hero Section",
        description: "Main hero section with title, subtitle, and call-to-action",
        icon: "Layout",
        category: "sections",
        fields: [
          {
            name: "title",
            label: "Title",
            type: "text" as const,
            required: true,
            placeholder: "Enter hero title",
          },
          {
            name: "subtitle",
            label: "Subtitle",
            type: "richText" as const,
            required: false,
            placeholder: "Enter hero subtitle",
          },
          {
            name: "heroImage",
            label: "Hero Image",
            type: "image" as const,
            required: false,
          },
          {
            name: "heroImageAlt",
            label: "Hero Image Alt Text",
            type: "text" as const,
            required: false,
            placeholder: "Alt text for hero image",
          },
          {
            name: "ctaText",
            label: "Primary CTA Button Text",
            type: "text" as const,
            required: false,
            defaultValue: "Get Started Today",
          },
          {
            name: "ctaUrl",
            label: "Primary CTA Button URL",
            type: "url" as const,
            required: false,
          },
          {
            name: "secondaryCtaText",
            label: "Secondary CTA Button Text",
            type: "text" as const,
            required: false,
            defaultValue: "View Our Work",
          },
          {
            name: "secondaryCtaUrl",
            label: "Secondary CTA Button URL",
            type: "url" as const,
            required: false,
          },
          {
            name: "badge1Text",
            label: "Badge 1 Text",
            type: "text" as const,
            required: false,
            defaultValue: "24/7 Support",
          },
          {
            name: "badge2Text",
            label: "Badge 2 Text",
            type: "text" as const,
            required: false,
            defaultValue: "Secure Solutions",
          },
          {
            name: "experienceYears",
            label: "Experience Years",
            type: "text" as const,
            required: false,
            defaultValue: "15+",
          },
          {
            name: "experienceLabel",
            label: "Experience Label",
            type: "text" as const,
            required: false,
            defaultValue: "Years\nExperience",
          },
        ],
        settings: {
          allowMultiple: false,
          isSystem: true,
          sortable: false,
        },
      },
      {
        name: "service_card",
        label: "Service Card",
        description: "Service or feature card with icon and description",
        icon: "Briefcase",
        category: "services",
        fields: [
          {
            name: "title",
            label: "Service Title",
            type: "text" as const,
            required: true,
          },
          {
            name: "slug",
            label: "URL Slug",
            type: "text" as const,
            required: true,
            placeholder: "cybersecurity-solutions",
          },
          {
            name: "description",
            label: "Short Description",
            type: "richText" as const,
            required: true,
          },
          {
            name: "fullDescription",
            label: "Full Description",
            type: "richText" as const,
            required: true,
            description: "Detailed description for the service detail page",
          },
          {
            name: "icon",
            label: "Icon",
            type: "text" as const,
            required: false,
            placeholder: "Lucide icon name",
          },
          {
            name: "image",
            label: "Service Image",
            type: "image" as const,
            required: false,
          },
          {
            name: "features",
            label: "Key Features",
            type: "array" as const,
            required: false,
            itemType: "text" as const,
          },
          {
            name: "benefits",
            label: "Benefits",
            type: "array" as const,
            required: false,
            itemType: "text" as const,
            description: "Benefits for the detailed service page",
          },
          {
            name: "order",
            label: "Display Order",
            type: "number" as const,
            required: false,
            defaultValue: 0,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: true,
        },
      },
      {
        name: "content_section",
        label: "Content Section",
        description: "General content section with title, content, and optional image",
        icon: "FileText",
        category: "sections",
        fields: [
          {
            name: "title",
            label: "Section Title",
            type: "text" as const,
            required: true,
          },
          {
            name: "subtitle",
            label: "Subtitle",
            type: "text" as const,
            required: false,
          },
          {
            name: "content",
            label: "Content",
            type: "richText" as const,
            required: true,
          },
          {
            name: "image",
            label: "Section Image",
            type: "image" as const,
            required: false,
          },
          {
            name: "backgroundColor",
            label: "Background Color",
            type: "color" as const,
            required: false,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: true,
        },
      },
      {
        name: "testimonial",
        label: "Testimonial",
        description: "Customer testimonial with quote, author, and company",
        icon: "Quote",
        category: "testimonials",
        fields: [
          {
            name: "quote",
            label: "Quote",
            type: "richText" as const,
            required: true,
          },
          {
            name: "author",
            label: "Author Name",
            type: "text" as const,
            required: true,
          },
          {
            name: "position",
            label: "Position/Title",
            type: "text" as const,
            required: false,
          },
          {
            name: "company",
            label: "Company",
            type: "text" as const,
            required: false,
          },
          {
            name: "avatar",
            label: "Author Photo",
            type: "image" as const,
            required: false,
          },
          {
            name: "rating",
            label: "Rating (1-5)",
            type: "number" as const,
            required: false,
            defaultValue: 5,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: true,
        },
      },
      {
        name: "contact_info",
        label: "Contact Information",
        description: "Contact details including address, phone, email, and hours",
        icon: "Phone",
        category: "contact",
        fields: [
          {
            name: "title",
            label: "Section Title",
            type: "text" as const,
            required: false,
            defaultValue: "Contact Us",
          },
          {
            name: "address",
            label: "Address",
            type: "richText" as const,
            required: false,
          },
          {
            name: "phone",
            label: "Phone Number",
            type: "text" as const,
            required: false,
          },
          {
            name: "email",
            label: "Email Address",
            type: "email" as const,
            required: false,
          },
          {
            name: "hours",
            label: "Business Hours",
            type: "richText" as const,
            required: false,
          },
          {
            name: "socialLinks",
            label: "Social Media Links",
            type: "array" as const,
            required: false,
          },
        ],
        settings: {
          allowMultiple: false,
          isSystem: false,
          sortable: false,
        },
      },
      {
        name: "feature_grid",
        label: "Feature Grid",
        description: "Grid of features or key points with icons and descriptions",
        icon: "Grid",
        category: "features",
        fields: [
          {
            name: "title",
            label: "Grid Title",
            type: "text" as const,
            required: false,
          },
          {
            name: "subtitle",
            label: "Grid Subtitle",
            type: "text" as const,
            required: false,
          },
          {
            name: "features",
            label: "Features",
            type: "array" as const,
            required: true,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: true,
        },
      },
      {
        name: "cta_section",
        label: "Call to Action Section",
        description: "Call to action section with title, description, and buttons",
        icon: "ArrowRight",
        category: "sections",
        fields: [
          {
            name: "title",
            label: "CTA Title",
            type: "text" as const,
            required: true,
          },
          {
            name: "description",
            label: "Description",
            type: "richText" as const,
            required: false,
          },
          {
            name: "primaryButtonText",
            label: "Primary Button Text",
            type: "text" as const,
            required: false,
          },
          {
            name: "primaryButtonUrl",
            label: "Primary Button URL",
            type: "url" as const,
            required: false,
          },
          {
            name: "secondaryButtonText",
            label: "Secondary Button Text",
            type: "text" as const,
            required: false,
          },
          {
            name: "secondaryButtonUrl",
            label: "Secondary Button URL",
            type: "url" as const,
            required: false,
          },
          {
            name: "backgroundColor",
            label: "Background Color",
            type: "color" as const,
            required: false,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: true,
        },
      },
      {
        name: "partner_logo",
        label: "Partner Logo",
        description: "Partner or client logo with optional link",
        icon: "Building",
        category: "partners",
        fields: [
          {
            name: "name",
            label: "Partner Name",
            type: "text" as const,
            required: true,
          },
          {
            name: "logo",
            label: "Logo Image",
            type: "image" as const,
            required: true,
          },
          {
            name: "url",
            label: "Partner Website",
            type: "url" as const,
            required: false,
          },
          {
            name: "description",
            label: "Description",
            type: "text" as const,
            required: false,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: true,
        },
      },
      {
        name: "page_content",
        label: "Page Content",
        description: "Full page content with title, content, and metadata",
        icon: "FileText",
        category: "pages",
        fields: [
          {
            name: "title",
            label: "Page Title",
            type: "text" as const,
            required: true,
          },
          {
            name: "subtitle",
            label: "Page Subtitle",
            type: "text" as const,
            required: false,
          },
          {
            name: "content",
            label: "Page Content",
            type: "richText" as const,
            required: true,
          },
          {
            name: "metaDescription",
            label: "Meta Description",
            type: "text" as const,
            required: false,
            placeholder: "SEO meta description",
          },
          {
            name: "featuredImage",
            label: "Featured Image",
            type: "image" as const,
            required: false,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: false,
        },
      },
      {
        name: "team_member",
        label: "Team Member",
        description: "Team member profile with photo and details",
        icon: "User",
        category: "team",
        fields: [
          {
            name: "name",
            label: "Full Name",
            type: "text" as const,
            required: true,
          },
          {
            name: "position",
            label: "Position/Title",
            type: "text" as const,
            required: true,
          },
          {
            name: "bio",
            label: "Biography",
            type: "richText" as const,
            required: false,
          },
          {
            name: "photo",
            label: "Profile Photo",
            type: "image" as const,
            required: false,
          },
          {
            name: "email",
            label: "Email",
            type: "email" as const,
            required: false,
          },
          {
            name: "phone",
            label: "Phone",
            type: "text" as const,
            required: false,
          },
          {
            name: "socialLinks",
            label: "Social Media Links",
            type: "array" as const,
            required: false,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: true,
        },
      },
      {
        name: "faq_item",
        label: "FAQ Item",
        description: "Frequently asked question with answer",
        icon: "HelpCircle",
        category: "faq",
        fields: [
          {
            name: "question",
            label: "Question",
            type: "text" as const,
            required: true,
          },
          {
            name: "answer",
            label: "Answer",
            type: "richText" as const,
            required: true,
          },
          {
            name: "category",
            label: "Category",
            type: "text" as const,
            required: false,
          },
          {
            name: "order",
            label: "Display Order",
            type: "number" as const,
            required: false,
            defaultValue: 0,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: true,
        },
      },
      {
        name: "value_card",
        label: "Value Card",
        description: "Company value or principle card with icon and description",
        icon: "Award",
        category: "about",
        fields: [
          {
            name: "title",
            label: "Value Title",
            type: "text" as const,
            required: true,
          },
          {
            name: "description",
            label: "Description",
            type: "richText" as const,
            required: true,
          },
          {
            name: "icon",
            label: "Icon",
            type: "text" as const,
            required: false,
            placeholder: "Lucide icon name (e.g., Target, Award, Users)",
          },
          {
            name: "order",
            label: "Display Order",
            type: "number" as const,
            required: false,
            defaultValue: 0,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: true,
        },
      },
      {
        name: "timeline_item",
        label: "Timeline Item",
        description: "Company history timeline item with year and milestone",
        icon: "Clock",
        category: "about",
        fields: [
          {
            name: "year",
            label: "Year",
            type: "text" as const,
            required: true,
            placeholder: "e.g., 2024",
          },
          {
            name: "title",
            label: "Milestone Title",
            type: "text" as const,
            required: true,
          },
          {
            name: "description",
            label: "Description",
            type: "richText" as const,
            required: true,
          },
          {
            name: "order",
            label: "Display Order",
            type: "number" as const,
            required: false,
            defaultValue: 0,
          },
        ],
        settings: {
          allowMultiple: true,
          isSystem: false,
          sortable: true,
        },
      },
    ];

    const results = [];
    const now = Date.now();

    for (const type of predefinedTypes) {
      // Check if already exists
      const existing = await ctx.db
        .query("contentTypes")
        .withIndex("by_name", (q) => q.eq("name", type.name))
        .first();

      if (!existing) {
        const id = await ctx.db.insert("contentTypes", {
          ...type,
          createdAt: now,
          updatedAt: now,
        });
        results.push({ name: type.name, id, status: "created" });
      } else {
        results.push({ name: type.name, id: existing._id, status: "exists" });
      }
    }

    return results;
  },
});
