import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Id } from "../../../../convex/_generated/dataModel";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Save, X, Eye, Globe } from "lucide-react";
import { toast } from "sonner";
import { RichTextEditor } from "./RichTextEditor";
import { ImageUploader } from "../media/ImageUploader";

interface ContentEditModalProps {
  contentId: Id<"content"> | null;
  isOpen: boolean;
  onClose: () => void;
  onSave?: (content: any) => void;
}

export const ContentEditModal = ({ 
  contentId, 
  isOpen, 
  onClose, 
  onSave 
}: ContentEditModalProps) => {
  const [formData, setFormData] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [activeLanguageTab, setActiveLanguageTab] = useState<string>("en");
  const [currentLanguage, setCurrentLanguage] = useState<string>("en");
  const [languageVersions, setLanguageVersions] = useState<Record<string, any>>({});

  // Queries
  const content = useQuery(
    api.content.getContentById,
    contentId ? { id: contentId } : "skip"
  );

  // Query for content in different language (when switching languages)
  const contentInLanguage = useQuery(
    api.content.getContent,
    content && currentLanguage !== content.language ? {
      identifier: content.identifier,
      language: currentLanguage,
      includeDraft: true
    } : "skip"
  );
  const contentType = useQuery(
    api.contentTypes.getContentTypeById,
    content?.contentTypeId ? { id: content.contentTypeId } : "skip"
  );

  // Mutations
  const updateContent = useMutation(api.content.updateContent);
  const publishContent = useMutation(api.content.publishContent);

  // Initialize form data when content loads
  useEffect(() => {
    if (content) {
      const versions: Record<string, any> = {};

      // Add the main content version
      versions[content.language] = {
        identifier: content.identifier,
        language: content.language,
        status: content.status,
        data: content.data || {},
        _id: content._id,
      };

      // Add content in other language if available
      if (contentInLanguage) {
        versions[contentInLanguage.language] = {
          identifier: contentInLanguage.identifier,
          language: contentInLanguage.language,
          status: contentInLanguage.status,
          data: contentInLanguage.data || {},
          _id: contentInLanguage._id,
        };
      }

      setLanguageVersions(versions);
      setActiveLanguageTab(content.language);
      setCurrentLanguage(content.language);

      // Set form data to the active language version
      setFormData(versions[content.language]);
    }
  }, [content, contentInLanguage]);

  // Update form data when switching language tabs
  useEffect(() => {
    if (languageVersions[activeLanguageTab]) {
      setFormData(languageVersions[activeLanguageTab]);
    } else {
      // Create new version for this language
      const baseContent = Object.values(languageVersions)[0];
      if (baseContent) {
        const newVersion = {
          identifier: baseContent.identifier,
          language: activeLanguageTab,
          status: "draft",
          data: {},
          _id: null, // New content
        };
        setLanguageVersions(prev => ({
          ...prev,
          [activeLanguageTab]: newVersion
        }));
        setFormData(newVersion);
      }
    }
  }, [activeLanguageTab, languageVersions]);

  // Handle form field changes
  const handleFieldChange = (fieldName: string, value: any) => {
    const updatedFormData = {
      ...formData,
      data: {
        ...formData.data,
        [fieldName]: value,
      },
    };

    setFormData(updatedFormData);

    // Update the language versions state
    setLanguageVersions(prev => ({
      ...prev,
      [activeLanguageTab]: updatedFormData
    }));
  };

  // Handle metadata changes
  const handleMetaChange = (field: string, value: any) => {
    if (field === "language") {
      setCurrentLanguage(value);
    } else {
      setFormData((prev: any) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // Save content
  const handleSave = async (shouldPublish = false) => {
    if (!contentId || !content) return;

    setIsLoading(true);
    try {
      await updateContent({
        id: contentId,
        data: formData.data,
        status: shouldPublish ? "published" : formData.status,
      });

      toast.success(
        shouldPublish ? "Content published successfully!" : "Content saved successfully!"
      );
      
      onSave?.(formData);
      onClose();
    } catch (error) {
      toast.error("Failed to save content");
      console.error("Save error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Render field based on type
  const renderField = (field: any) => {
    const value = formData.data?.[field.name] || field.defaultValue || "";

    switch (field.type) {
      case "text":
        return (
          <Input
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case "richText":
        return (
          <RichTextEditor
            value={value}
            onChange={(content) => handleFieldChange(field.name, content)}
            placeholder={field.placeholder}
          />
        );

      case "textarea":
        return (
          <Textarea
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
            rows={4}
          />
        );

      case "number":
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleFieldChange(field.name, Number(e.target.value))}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case "email":
        return (
          <Input
            type="email"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case "url":
        return (
          <Input
            type="url"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case "image":
        return (
          <ImageUploader
            value={value}
            onChange={(imageData) => handleFieldChange(field.name, imageData)}
            accept="image/*"
          />
        );

      case "color":
        return (
          <Input
            type="color"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            required={field.required}
          />
        );

      default:
        return (
          <Input
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );
    }
  };

  if (!content || !contentType) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl font-bold">
                Edit {contentType.label}
              </DialogTitle>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant={formData.status === "published" ? "default" : "secondary"}>
                  {formData.status}
                </Badge>
                <span className="text-sm text-gray-500">
                  {formData.identifier}
                </span>
              </div>
            </div>
          </div>
        </DialogHeader>

        {/* Language Tabs */}
        <div className="flex-1 overflow-y-auto py-4">
          <Tabs value={activeLanguageTab} onValueChange={setActiveLanguageTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="en" className="flex items-center gap-2">
                🇺🇸 English
                {languageVersions.en && (
                  <Badge variant="outline" className="text-xs">
                    {languageVersions.en.status}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="es" className="flex items-center gap-2">
                🇪🇸 Spanish
                {languageVersions.es && (
                  <Badge variant="outline" className="text-xs">
                    {languageVersions.es.status}
                  </Badge>
                )}
                {!languageVersions.es && (
                  <Badge variant="outline" className="text-xs text-amber-600">
                    New
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="en" className="mt-6">
              <div className="space-y-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">English Content</h3>
                  {languageVersions.en && (
                    <Badge variant={languageVersions.en.status === "published" ? "default" : "secondary"}>
                      {languageVersions.en.status}
                    </Badge>
                  )}
                </div>
                {contentType.fields?.map((field: any) => (
                  <div key={field.name} className="space-y-2">
                    <Label htmlFor={field.name} className="text-sm font-medium">
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </Label>
                    {renderField(field)}
                    {field.description && (
                      <p className="text-xs text-gray-500">{field.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="es" className="mt-6">
              <div className="space-y-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Spanish Content</h3>
                  {languageVersions.es ? (
                    <Badge variant={languageVersions.es.status === "published" ? "default" : "secondary"}>
                      {languageVersions.es.status}
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-amber-600">
                      New Translation
                    </Badge>
                  )}
                </div>
                {contentType.fields?.map((field: any) => (
                  <div key={field.name} className="space-y-2">
                    <Label htmlFor={field.name} className="text-sm font-medium">
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </Label>
                    {renderField(field)}
                    {field.description && (
                      <p className="text-xs text-gray-500">{field.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
          </div>
          
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => handleSave(false)}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              Save Draft
            </Button>
            
            <Button
              onClick={() => handleSave(true)}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Globe className="w-4 h-4 mr-2" />
              )}
              Publish
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
