
import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Globe, User, LogIn, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SignInButton, UserButton } from '@clerk/clerk-react';
import { useAuth } from '@/hooks/useAuth';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';
import { EditModeToggle, useContentContext } from '@/components/content/ContentProvider';
import { useTranslation, useLanguage } from '@/contexts/I18nContext';
import { ServicesDropdown } from '@/components/ServicesDropdown';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const Layout = ({ children }: { children: React.ReactNode }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const { isSignedIn, user, canEditContent } = useAuth();
  const { t } = useTranslation();
  const { language } = useLanguage();

  // Try to get content context, but don't fail if not available
  let contentContext;
  try {
    contentContext = useContentContext();
  } catch {
    // Not within ContentProvider, that's okay
    contentContext = null;
  }

  const navItems = [
    { name: t('nav.home'), href: '/' },
    { name: t('nav.networkSolutions'), href: '/network-solutions' },
    { name: t('nav.about'), href: '/about' },
    { name: t('nav.contact'), href: '/contact' },
  ];

  const isActive = (href: string) => {
    return location.pathname === href;
  };

  const isServicesActive = () => {
    return location.pathname === '/services' || location.pathname.startsWith('/services/');
  };



  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white/95 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              <img
                src="/logo/officetech_LOGO.png"
                alt="OfficeTech Logo"
                className="h-8 w-auto"
              />
              <span className="font-bold text-xl text-gray-900">OfficeTech</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  to={item.href}
                  className={`font-medium transition-colors duration-200 ${
                    isActive(item.href)
                      ? 'text-blue-600'
                      : 'text-gray-700 hover:text-blue-600'
                  }`}
                >
                  {item.name}
                </Link>
              ))}

              {/* Services Dropdown */}
              <ServicesDropdown
                isActive={isServicesActive()}
                className="font-medium transition-colors duration-200"
              />

              {/* Language Toggle */}
              <LanguageSwitcher />

              {/* Authentication */}
              {isSignedIn ? (
                <div className="flex items-center space-x-4">
                  {canEditContent && contentContext && (
                    <EditModeToggle />
                  )}
                  {canEditContent && (
                    <Link to="/admin">
                      <Button variant="outline" size="sm">
                        <Settings className="w-4 h-4 mr-1" />
                        Admin
                      </Button>
                    </Link>
                  )}
                  <UserButton
                    appearance={{
                      elements: {
                        avatarBox: "w-8 h-8",
                      }
                    }}
                  />
                </div>
              ) : (
                <SignInButton mode="modal">
                  <Button variant="outline" size="sm">
                    <LogIn className="w-4 h-4 mr-1" />
                    Sign In
                  </Button>
                </SignInButton>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-100 animate-fade-in">
              <div className="flex flex-col space-y-4">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    to={item.href}
                    className={`font-medium transition-colors duration-200 ${
                      isActive(item.href)
                        ? 'text-blue-600'
                        : 'text-gray-700 hover:text-blue-600'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}

                {/* Mobile Services Link */}
                <Link
                  to="/services"
                  className={`font-medium transition-colors duration-200 ${
                    isServicesActive()
                      ? 'text-blue-600'
                      : 'text-gray-700 hover:text-blue-600'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {t('nav.services')}
                </Link>

                <LanguageSwitcher />

                {/* Mobile Authentication */}
                <div className="pt-4 border-t border-gray-200">
                  {isSignedIn ? (
                    <div className="space-y-2">
                      <div className="flex items-center space-x-3 px-2">
                        <UserButton
                          appearance={{
                            elements: {
                              avatarBox: "w-8 h-8",
                            }
                          }}
                        />
                        <span className="text-sm text-gray-700">
                          {user?.firstName} {user?.lastName}
                        </span>
                      </div>
                      {canEditContent && contentContext && (
                        <EditModeToggle className="w-full justify-start" />
                      )}
                      {canEditContent && (
                        <Link to="/admin" onClick={() => setIsMenuOpen(false)}>
                          <Button variant="outline" size="sm" className="w-full justify-start">
                            <Settings className="w-4 h-4 mr-2" />
                            Admin Dashboard
                          </Button>
                        </Link>
                      )}
                    </div>
                  ) : (
                    <SignInButton mode="modal">
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <LogIn className="w-4 h-4 mr-2" />
                        Sign In
                      </Button>
                    </SignInButton>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Main Content */}
      <main>{children}</main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <img
                  src="/logo/officetech_LOGO.png"
                  alt="OfficeTech Logo"
                  className="h-8 w-auto"
                />
                {/* <span className="font-bold text-xl">OfficeTech Guinea</span> */}
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                {language === 'en'
                  ? 'Leading technology and security solutions in Equatorial Guinea. Protecting and connecting your business.'
                  : 'Líderes en soluciones tecnológicas y de seguridad en Guinea Ecuatorial. Protegiendo y conectando tu negocio.'
                }
              </p>
              <Button asChild className="bg-blue-600 hover:bg-blue-700">
                <Link to="/contact">
                  {language === 'en' ? 'Get Started' : 'Comenzar'}
                </Link>
              </Button>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="font-semibold mb-4">
                {language === 'en' ? 'Quick Links' : 'Enlaces'}
              </h3>
              <ul className="space-y-2">
                {navItems.map((item) => (
                  <li key={item.href}>
                    <Link
                      to={item.href}
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="font-semibold mb-4">
                {language === 'en' ? 'Contact' : 'Contacto'}
              </h3>
              <div className="space-y-2 text-gray-400">
                <p>Malabo, Equatorial Guinea</p>
                <p>+240 XXX XXX XXX</p>
                <p><EMAIL></p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 OfficeTech Guinea. {language === 'en' ? 'All rights reserved.' : 'Todos los derechos reservados.'}</p>
          </div>
        </div>
      </footer>


    </div>
  );
};

export default Layout;
