
import Layout from '@/components/Layout';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/contexts/I18nContext';
import { DynamicContent } from '@/components/content/DynamicContent';
import { ContentProvider } from '@/components/content/ContentProvider';
import { DynamicServiceCards } from '@/components/DynamicServiceCards';

const ServicesContent = () => {
  const { t } = useTranslation();

  return (
    <>
      {/* Dynamic Services Hero Section */}
      <DynamicContent
        identifier="services-hero"
        className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20"
        fallback={
          <section className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                <span className="text-gradient">{t('services.title')}</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {t('services.subtitle')}
              </p>
            </div>
          </section>
        }
      />

      {/* Dynamic Services Grid */}
      <DynamicServiceCards
        variant="services"
        title="Our Main Services"
        subtitle="Comprehensive technology solutions organized into key service areas"
        editable={true}
      />

      {/* CTA Section */}
      <section className="bg-gray-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Contact us today to discuss your technology and security needs. 
            Our expert team is ready to help you find the perfect solution.
          </p>
          <Button 
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg"
          >
            Contact Us Today
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </section>
    </>
  );
};

const Services = () => {
  return (
    <ContentProvider defaultLanguage="en">
      <Layout>
        <ServicesContent />
      </Layout>
    </ContentProvider>
  );
};

export default Services;
